#!/usr/bin/env python3
"""
调试引用消息处理逻辑
"""

# 模拟当前的逻辑
def is_quote_message(content: str) -> bool:
    """检查是否是引用消息"""
    quote_indicators = [
        "这是一条引用消息", 
        "这是一个图片引用消息", 
        "这是一个文件引用消息", 
        "这是一个语音引用消息",
        "引用:"
    ]
    return any(indicator in content for indicator in quote_indicators)

def parse_quote_message(content: str) -> tuple[str, str]:
    """解析引用消息，返回(引用内容, 新消息内容)"""
    try:
        # 标准引用格式
        if "引用:" in content:
            parts = content.split("引用:", 1)
            if len(parts) == 2:
                new_content = parts[0].strip()
                quoted_content = parts[1].strip()
                return quoted_content, new_content

        # 处理包含引用标识的消息
        quote_indicators = ["这是一条引用消息", "这是一个图片引用消息", "这是一个文件引用消息", "这是一个语音引用消息"]
        for indicator in quote_indicators:
            if indicator in content:
                # 移除引用标识，获取用户的实际查询内容
                new_content = content.replace(indicator, "").strip()
                quoted_content = f"[引用了一个{indicator.replace('这是一个', '').replace('这是一条', '')}]"
                return quoted_content, new_content

        return "", content.strip()
    except Exception as e:
        print(f"解析引用消息失败: {e}")
        return "", content.strip()

def check_wakeup_words(content: str, wakeup_words: dict) -> tuple[bool, str, str]:
    """检查唤醒词"""
    for wakeup_word, model_config in wakeup_words.items():
        wakeup_lower = wakeup_word.lower()
        content_lower = content.lower()
        if content_lower.startswith(wakeup_lower) or f" {wakeup_lower}" in content_lower:
            # 移除唤醒词
            if content_lower.startswith(wakeup_lower):
                processed_query = content[len(wakeup_word):].strip()
            else:
                processed_query = content.replace(wakeup_word, "", 1).strip()
            
            if not processed_query:
                processed_query = "你好"
            
            return True, model_config, processed_query
    return False, None, content

# 测试实际日志中的消息
test_cases = [
    # 第一条消息：普通唤醒词消息
    "吒儿 你好啊",

    # 第二条消息：引用文字消息
    "吒儿 是这些人吗 引用:{'MsgType': 1, 'NewMsgId': '2705194278910908508', 'ToWxid': '48321594332@chatroom', 'FromWxid': 'wxid_b11j5ib2urox22', 'Nickname': '吒儿', 'MsgSource': '<msgsource><atuserlist>flyhunterl</atuserlist><bizflag>0</bizflag><silence>1</silence><membercount>2</membercount><signature>N0_V1_z1synsoM|v1_N34Lx/1+</signature><tmp_node><publisher-id></publisher-id></tmp_node></msgsource>', 'Content': '@flynn\\u2005哎哟喂，今天（2025-06-10）是第一组扫地噻！组员有田诗琦、周曰停、灵活协调、邓煜坤、曾桦瑞、杜粽浩、罗琳瑞这几个娃娃。记得喊他们莫偷懒哈，教室要整得巴适哦！ 😄', 'Createtime': '1749535533'}",

    # 第三条消息：引用图片消息
    "这啥意思 引用:{'MsgType': 3, 'NewMsgId': '2055007059511738150', 'ToWxid': '48321594332@chatroom', 'FromWxid': 'wxid_b11j5ib2urox22', 'Nickname': '吒儿', 'MsgSource': '<msgsource><sec_msg_node><uuid>ff411f1c8701b68d0829ceeaa51df640_</uuid><risk-file-flag /><risk-file-md5-list /></sec_msg_node><silence>1</silence><membercount>2</membercount><NotAutoDownloadRange>20:00-22:00;00:00-01:00</NotAutoDownloadRange><signature>N0_V1_Z8T/sF1R|v1_NYBadLk9</signature><tmp_node><publisher-id></publisher-id></tmp_node></msgsource>', 'Content': '<?xml version=\"1.0\"?><msg><img aeskey=\"699ad66694bc60e7bd73ff0b68d3d613\" encryver=\"1\" cdnthumbaeskey=\"699ad66694bc60e7bd73ff0b68d3d613\" cdnthumburl=\"3057020100044b304902010002048f4354e302032f54690204079999db0204685e11c3042439373230623862322d616434662d346336352d396435322d313838623731633232633466020405250a020201000405004c4dfd00\" cdnthumblength=\"5918\" cdnthumbheight=\"432\" cdnthumbwidth=\"194\" cdnmidheight=\"0\" cdnmidwidth=\"0\" cdnhdheight=\"0\" cdnhdwidth=\"0\" cdnmidimgurl=\"3057020100044b304902010002048f4354e302032f54690204079999db0204685e11c3042439373230623862322d616434662d346336352d396435322d313838623731633232633466020405250a020201000405004c4dfd00\" length=\"298666\" md5=\"53936319071fc19a63d539e03d08bad0\" hevc_mid_size=\"121131\" originsourcemd5=\"6eea83bf558cceefe2c1c5b2476ec580\"><secHashInfoBase64>eyJwaGFzaCI6IjUwMTFkNDYwMTAwMDEwMTAiLCJwZHFIYXNoIjoiOTFlOGU4OTcwZTdmYjVkYWE1OWFkODc1ZWE5NzcyMDE1ZTIwNDE4NzJhZmMwZjc5ODFjYTU1NmEyYTlmYWE5NCJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>', 'Createtime': '1750995964', 'cdnthumbaeskey': '699ad66694bc60e7bd73ff0b68d3d613'}"
]

wakeup_words = {"吒儿": "通用助手模型"}

print("调试实际日志中的引用消息处理:")
print("=" * 80)

for i, test_content in enumerate(test_cases, 1):
    print(f"\n测试案例 {i}: {test_content[:50]}...")
    print("-" * 60)

    # 步骤1: 检查是否是引用消息
    is_quote = is_quote_message(test_content)
    print(f"1. 是否引用消息: {is_quote}")

    if is_quote:
        # 步骤2: 解析引用消息
        quoted_content, new_content = parse_quote_message(test_content)
        print(f"2. 解析结果:")
        print(f"   - 引用内容: {quoted_content[:100] if quoted_content else '无'}...")
        print(f"   - 新消息内容: '{new_content}'")

        # 步骤3: 检查唤醒词
        has_wakeup, model, processed_query = check_wakeup_words(new_content, wakeup_words)
        print(f"3. 唤醒词检查:")
        print(f"   - 检测到唤醒词: {has_wakeup}")
        print(f"   - 模型: {model}")
        print(f"   - 处理后查询: '{processed_query}'")

        if has_wakeup:
            # 步骤4: 构建完整查询
            if quoted_content:
                full_query = f"引用内容：{quoted_content}\n\n用户问题：{processed_query}"
            else:
                full_query = processed_query
            print(f"4. 完整查询: {full_query[:200]}...")
            print("✅ 应该触发AI回复")
        else:
            print("❌ 未检测到唤醒词，不会触发AI回复")
    else:
        # 普通消息处理
        has_wakeup, model, processed_query = check_wakeup_words(test_content, wakeup_words)
        print(f"2. 普通消息唤醒词检查:")
        print(f"   - 检测到唤醒词: {has_wakeup}")
        print(f"   - 模型: {model}")
        print(f"   - 处理后查询: '{processed_query}'")

        if has_wakeup:
            print("✅ 应该触发AI回复")
        else:
            print("❌ 未检测到唤醒词，不会触发AI回复")

print("\n" + "=" * 80)
print("总结:")
print("- 第一条消息：普通唤醒词消息，应该正常触发")
print("- 第二条消息：引用消息+唤醒词，修复后应该能触发")
print("- 第三条消息：引用消息但无唤醒词，不应该触发（符合预期）")
