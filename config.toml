[FastGptx]
enable = true
default-model = "通用助手"
commands = ["聊天", "AI", "重置对话", "#reset"]
command-tip = """
-----XXXBot-----
💬FastGPT AI聊天指令：
1. 切换模型（将会一直保持到下次切换）：
   - @通用助手 切换：切换到通用助手模型
   - @代码助手 切换：切换到代码助手模型
2. 临时使用其他模型：
   - 通用助手 消息内容：临时使用通用助手模型
   - 代码助手 消息内容：临时使用代码助手模型
3. 重置对话：
   - 重置对话：清除当前对话历史，开始新的对话
   - #reset：快速重置对话，即使在1分钟内也会强制开始新对话"""
admin_ignore = true
whitelist_ignore = true
http-proxy = ""
voice_reply_all = false
robot-names = ["吒儿"]
remember_user_model = true
# 全局优先级设置 (0-99)，值越高优先级越高
priority = 25

[FastGptx.models."通用助手"]
api-key = "fastgpt-eGGN5zFNImzeQ389KHyjU4vjHU5tgMwS6zWZZKtdyBwRbjlJvciY"
base-url = "https://fast.llingfei.com/api"
app-id = "your-app-id-here"
trigger-words = ["@倔驴"]
wakeup-words = ["倔驴"]
price = 1

[FastGptx.models."代码助手"]
api-key = "fastgpt-your-api-key-here"
base-url = "https://fast.llingfei.com/api"
app-id = "your-code-app-id-here"
trigger-words = ["@代码助手"]
wakeup-words = ["代码助手"]
price = 2

[FastGptx.models."翻译助手"]
api-key = "fastgpt-your-api-key-here"
base-url = "https://fast.llingfei.com/api"
app-id = "your-translate-app-id-here"
trigger-words = ["@翻译助手"]
wakeup-words = ["翻译助手", "翻译"]
price = 1
