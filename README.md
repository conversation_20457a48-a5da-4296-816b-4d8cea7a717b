# FastGptx 插件 🚀

## 简介

FastGptx 插件是为 XXXBot 机器人框架设计的一个插件，它允许机器人与 FastGPT (一个开源的 LLM 应用开发平台) 进行交互。通过这个插件，你可以让你的微信机器人具备强大的自然语言处理能力。

## 特性

- **多消息类型支持:** 支持文本、@消息、语音、图片、视频和文件消息的处理。💬
- **FastGPT 集成:** 无缝对接 FastGPT 平台，利用其强大的 LLM 能力。🔗
- **多模型支持:** 支持配置多个 FastGPT 应用，可以切换不同的助手。🧠
- **灵活的配置:** 允许配置 API 密钥、基础 URL、命令、触发词、价格、代理等。⚙️
- **积分系统集成:** 可以配置是否管理员和白名单用户忽略积分检查。💰
- **唤醒词机制:** 支持通过特定词汇快速切换或临时使用不同模型。✨
- **图片缓存:** 支持图片消息的缓存和处理。🖼️
- **错误处理:** 完善的错误处理机制，当 FastGPT 返回错误时，能向用户提供清晰的错误信息。⚠️

## 安装

1. 确保你已经安装了 XXXBot 机器人框架。 ✅
2. 将 `FastGptx` 插件文件夹复制到 XXXBot 的 `plugins` 目录下。 📁

## 配置

1. 编辑 `main_config.toml` 文件，配置管理员列表：

    ```toml
    [XYBot]
    admins = ["your_wxid"] # 你的微信ID
    ```

2. 编辑 `plugins/FastGptx/config.toml` 文件，配置 FastGptx 插件：

    ```toml
    [FastGptx]
    enable = true          # 是否启用插件
    default-model = "通用助手"  # 默认使用的模型
    commands = ["聊天", "AI", "重置对话"]
    command-tip = """
    -----XXXBot-----
    💬FastGPT AI聊天指令：
    1. 切换模型（将会一直保持到下次切换）：
       - @通用助手 切换：切换到通用助手模型
       - @代码助手 切换：切换到代码助手模型
    2. 临时使用其他模型：
       - 通用助手 消息内容：临时使用通用助手模型
       - 代码助手 消息内容：临时使用代码助手模型
    3. 重置对话：
       - 重置对话：清除当前对话历史，开始新的对话"""
    admin_ignore = true            # 管理员是否免积分
    whitelist_ignore = true        # 白名单用户是否免积分
    http-proxy = ""                # HTTP代理配置
    voice_reply_all = false        # 是否总是使用语音回复
    robot-names = ["毛球", "🥥", "智能助手", "小助手"]
    remember_user_model = true     # 是否记住用户选择的模型
    priority = 25                  # 插件优先级

    [FastGptx.models."通用助手"]
    api-key = "fastgpt-your-api-key-here"
    base-url = "https://fast.llingfei.com/api"
    app-id = "your-app-id-here"
    trigger-words = ["@通用助手"]
    wakeup-words = ["通用助手"]
    price = 1

    [FastGptx.models."代码助手"]
    api-key = "fastgpt-your-api-key-here"
    base-url = "https://fast.llingfei.com/api"
    app-id = "your-code-app-id-here"
    trigger-words = ["@代码助手"]
    wakeup-words = ["代码助手"]
    price = 2
    ```

## 使用方法

1. 在微信中向机器人发送配置的命令，例如 `AI 你好` 或者 `@机器人 AI 你好` (在群聊中)。💬
2. 机器人会将你的消息发送到 FastGPT，并将 FastGPT 的回复返回给你。 🤖
3. 使用唤醒词可以快速切换模型，例如 `通用助手 帮我写一首诗`。 🎯

### 模型切换

- **永久切换**: 发送 `@通用助手 切换` 来永久切换到通用助手模型
- **临时使用**: 发送 `代码助手 写一个Python函数` 来临时使用代码助手模型
- **唤醒词**: 直接发送 `通用助手 你好` 来使用通用助手回复

### 重置对话

发送 `重置对话` 命令可以清除当前的对话历史，开始全新的对话。

## 消息类型支持

- **文本消息:** 直接发送文本消息给机器人。 📝
- **@消息:** 在群聊中 @ 机器人并发送消息。 📢
- **图片消息:** 发送图片消息给机器人（会被缓存用于后续对话）。 🖼️
- **语音消息:** 发送语音消息给机器人（需要语音转文字功能）。 🎤

## 积分系统

- 插件使用了 XXXBot 的积分系统来管理用户的使用权限。 📊
- 每次调用 FastGPT 插件会消耗用户一定数量的积分（可在配置文件中设置）。 💸
- 管理员和白名单用户可以配置为忽略积分检查。 🛡️

## API 配置说明

### FastGPT API 配置

- **api-key**: 你的 FastGPT API 密钥
- **base-url**: FastGPT 服务的基础 URL，格式为 `https://fast.llingfei.com/api`
- **app-id**: FastGPT 应用的 ID
- **trigger-words**: 触发词列表，用于在群聊中触发特定模型
- **wakeup-words**: 唤醒词列表，用于快速切换或临时使用模型
- **price**: 使用该模型消耗的积分数量

## 依赖

- XXXBot 机器人框架
- `aiohttp`
- `loguru`
- `tomllib` (Python 3.11+) or `toml` (Python < 3.11)
- `WechatAPI`
- `database.XYBotDB`
- `utils.decorators`
- `utils.plugin_base`

## 注意事项

- 请确保你的 FastGPT API 密钥和基础 URL 配置正确。🔑
- 确保 FastGPT 服务正常运行并且可以访问。 🌐
- 如果遇到问题，请查看 XXXBot 的日志文件 `logs/xybot.log`。 🔍

## 作者

- XXXBot开发团队 👨‍💻

## 版本历史

- **1.0.0** 初始版本 🐣
  - 基本的 FastGPT 集成功能
  - 多模型支持
  - 唤醒词机制
  - 积分系统集成

## License

MIT 📜
