#!/usr/bin/env python3
"""
测试引用消息修复
"""

def is_quote_message(content):
    """检查是否是引用消息"""
    quote_indicators = [
        "引用:",
        "这是一条引用消息", 
        "这是一个图片引用消息", 
        "这是一个文件引用消息", 
        "这是一个语音引用消息"
    ]
    return any(indicator in content for indicator in quote_indicators)

def parse_quote_message(content):
    """解析引用消息，返回(引用内容, 新消息内容)"""
    try:
        # 标准引用格式：用户内容 引用:{...}
        if "引用:" in content:
            parts = content.split("引用:", 1)
            if len(parts) == 2:
                new_content = parts[0].strip()
                quoted_content = parts[1].strip()
                return quoted_content, new_content

        # 处理包含引用标识的消息
        quote_indicators = ["这是一条引用消息", "这是一个图片引用消息", "这是一个文件引用消息", "这是一个语音引用消息"]
        for indicator in quote_indicators:
            if indicator in content:
                # 移除引用标识，获取用户的实际查询内容
                new_content = content.replace(indicator, "").strip()
                quoted_content = f"[引用了一个{indicator.replace('这是一个', '').replace('这是一条', '')}]"
                return quoted_content, new_content

        return "", content.strip()
    except Exception as e:
        print(f"解析引用消息失败: {e}")
        return "", content.strip()

def check_wakeup_words(content, wakeup_words):
    """检查唤醒词"""
    for wakeup_word, model_config in wakeup_words.items():
        wakeup_lower = wakeup_word.lower()
        content_lower = content.lower()
        if content_lower.startswith(wakeup_lower) or f" {wakeup_lower}" in content_lower:
            # 移除唤醒词
            if content_lower.startswith(wakeup_lower):
                processed_query = content[len(wakeup_word):].strip()
            else:
                processed_query = content.replace(wakeup_word, "", 1).strip()
            
            if not processed_query:
                processed_query = "你好"
            
            return True, model_config, processed_query
    return False, None, content

# 测试实际日志中的消息
test_cases = [
    # 第一条消息：普通唤醒词消息
    "吒儿 你好啊",
    
    # 第二条消息：引用文字消息
    "吒儿 是这些人吗 引用:{'MsgType': 1, 'Content': '扫地安排'}",
    
    # 第三条消息：引用图片消息
    "这啥意思 引用:{'MsgType': 3, 'Content': '图片内容'}"
]

wakeup_words = {"吒儿": "通用助手模型"}

print("调试引用消息处理:")
print("=" * 60)

for i, test_content in enumerate(test_cases, 1):
    print(f"\n测试案例 {i}: {test_content[:40]}...")
    print("-" * 50)
    
    # 步骤1: 检查是否是引用消息
    is_quote = is_quote_message(test_content)
    print(f"1. 是否引用消息: {is_quote}")
    
    if is_quote:
        # 步骤2: 解析引用消息
        quoted_content, new_content = parse_quote_message(test_content)
        print(f"2. 解析结果:")
        print(f"   - 引用内容: {quoted_content[:50]}...")
        print(f"   - 新消息内容: '{new_content}'")
        
        # 步骤3: 检查唤醒词
        has_wakeup, model, processed_query = check_wakeup_words(new_content, wakeup_words)
        print(f"3. 唤醒词检查:")
        print(f"   - 检测到唤醒词: {has_wakeup}")
        print(f"   - 模型: {model}")
        print(f"   - 处理后查询: '{processed_query}'")
        
        if has_wakeup:
            # 步骤4: 构建完整查询
            if quoted_content:
                full_query = f"引用内容：{quoted_content}\n\n用户问题：{processed_query}"
            else:
                full_query = processed_query
            print(f"4. 完整查询: {full_query[:100]}...")
            print("✅ 应该触发AI回复")
        else:
            print("❌ 未检测到唤醒词，不会触发AI回复")
    else:
        # 普通消息处理
        has_wakeup, model, processed_query = check_wakeup_words(test_content, wakeup_words)
        print(f"2. 普通消息唤醒词检查:")
        print(f"   - 检测到唤醒词: {has_wakeup}")
        print(f"   - 模型: {model}")
        print(f"   - 处理后查询: '{processed_query}'")
        
        if has_wakeup:
            print("✅ 应该触发AI回复")
        else:
            print("❌ 未检测到唤醒词，不会触发AI回复")

print("\n" + "=" * 60)
print("总结:")
print("- 第一条消息：普通唤醒词消息，应该正常触发")
print("- 第二条消息：引用消息+唤醒词，修复后应该能触发")
print("- 第三条消息：引用消息但无唤醒词，不应该触发（符合预期）")
print("\n修复要点:")
print("1. 添加了 is_quote_message() 和 parse_quote_message() 方法")
print("2. 在 handle_text() 中检测并解析引用消息")
print("3. 在发送给AI时构建完整的查询内容（引用内容+用户问题）")
print("4. 支持私聊、群聊和@消息中的引用消息处理")
