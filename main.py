import io
import json
import re
import tomllib
from typing import Optional, Union, Dict, List, Tuple
import time
from dataclasses import dataclass, field
from datetime import datetime
import asyncio
from collections import defaultdict
import urllib.parse
import mimetypes
import base64
import uuid

import aiohttp
import filetype
from loguru import logger
import speech_recognition as sr
import os
from WechatAPI import WechatAPIClient
from database.XYBotDB import XYBotDB
from utils.decorators import *
from utils.plugin_base import PluginBase
from gtts import gTTS
import traceback
import shutil
from PIL import Image
import xml.etree.ElementTree as ET

# 添加API代理导入
try:
    from api_manager_integrator import has_api_manager_feature
    has_api_proxy = has_api_manager_feature()
    if has_api_proxy:
        logger.info("API管理中心可用，FastGptx插件将使用API代理")
    else:
        logger.info("API管理中心不可用，FastGptx插件将使用直接连接")
except ImportError:
    has_api_proxy = False
    logger.warning("未找到API管理中心集成模块，FastGptx插件将使用直接连接")

# 常量定义
XYBOT_PREFIX = "-----XXXBot-----\n"
FASTGPT_ERROR_MESSAGE = "🙅对不起，FastGPT出现错误！\n"
VOICE_TRANSCRIPTION_FAILED = "\n语音转文字失败"
TEXT_TO_VOICE_FAILED = "\n文本转语音失败"

@dataclass
class ModelConfig:
    api_key: str
    base_url: str
    app_id: str
    trigger_words: list[str]
    wakeup_words: list[str] = field(default_factory=list)

class FastGptx(PluginBase):
    description = "FastGPT插件"
    author = "XXXBot开发团队"
    version = "1.0.0"
    is_ai_platform = True  # 标记为 AI 平台插件

    def __init__(self):
        super().__init__()
        self.user_models = {}  # 存储用户当前使用的模型
        self.processed_messages = {}  # 存储已处理的消息ID，避免重复处理
        self.message_expiry = 60  # 消息处理记录的过期时间（秒）

        # 添加会话管理
        self.conversation_sessions = {}  # 存储会话ID和时间戳: {user_id: {"chat_id": str, "timestamp": float}}
        self.conversation_timeout = 60  # 会话超时时间（秒），1分钟内的对话保持连续
        
        try:
            with open("main_config.toml", "rb") as f:
                config = tomllib.load(f)
            self.admins = config["XYBot"]["admins"]
        except (FileNotFoundError, tomllib.TOMLDecodeError) as e:
            logger.error(f"加载主配置文件失败: {e}")
            raise

        try:
            with open("plugins/FastGptx/config.toml", "rb") as f:
                config = tomllib.load(f)
            plugin_config = config["FastGptx"]
            self.enable = plugin_config["enable"]
            self.default_model = plugin_config["default-model"]
            self.command_tip = plugin_config["command-tip"]
            self.commands = plugin_config["commands"]
            self.admin_ignore = plugin_config["admin_ignore"]
            self.whitelist_ignore = plugin_config["whitelist_ignore"]
            self.http_proxy = plugin_config["http-proxy"]
            self.voice_reply_all = plugin_config["voice_reply_all"]
            self.robot_names = plugin_config.get("robot-names", [])
            self.remember_user_model = plugin_config.get("remember_user_model", True)

            # 加载所有模型配置
            self.models = {}
            for model_name, model_config in plugin_config.get("models", {}).items():
                self.models[model_name] = ModelConfig(
                    api_key=model_config["api-key"],
                    base_url=model_config["base-url"],
                    app_id=model_config["app-id"],
                    trigger_words=model_config["trigger-words"],
                    wakeup_words=model_config.get("wakeup-words", [])
                )

            # 设置当前使用的模型
            self.current_model = self.models[self.default_model]
        except (FileNotFoundError, tomllib.TOMLDecodeError) as e:
            logger.error(f"加载FastGptx插件配置文件失败: {e}")
            raise

        self.db = XYBotDB()
        self.image_cache = {}  # 改为存储图片列表: {user_id: [{"content": bytes, "timestamp": float}, ...]}
        self.image_cache_timeout = 60
        # 添加文件缓存
        self.file_cache = {}
        self.file_cache_timeout = 300  # 5分钟文件缓存超时
        # 添加文件存储目录配置
        self.files_dir = "files"
        # 创建文件存储目录
        os.makedirs(self.files_dir, exist_ok=True)
        # 创建临时文件目录
        os.makedirs("temp", exist_ok=True)

        # 创建唤醒词到模型的映射
        self.wakeup_word_to_model = {}
        logger.info("开始加载唤醒词配置:")
        for model_name, model_config in self.models.items():
            logger.info(f"处理模型 '{model_name}' 的唤醒词列表: {model_config.wakeup_words}")
            for wakeup_word in model_config.wakeup_words:
                if wakeup_word in self.wakeup_word_to_model:
                    old_model = next((name for name, config in self.models.items()
                                     if config == self.wakeup_word_to_model[wakeup_word]), '未知')
                    logger.warning(f"唤醒词冲突! '{wakeup_word}' 已绑定到模型 '{old_model}'，"
                                  f"现在被覆盖绑定到 '{model_name}'")
                self.wakeup_word_to_model[wakeup_word] = model_config
                logger.info(f"唤醒词 '{wakeup_word}' 成功绑定到模型 '{model_name}'")

        logger.info(f"唤醒词映射完成，共加载 {len(self.wakeup_word_to_model)} 个唤醒词")

        # 加载配置文件
        self.config_path = os.path.join(os.path.dirname(__file__), "config.toml")
        logger.info(f"加载FastGptx插件配置文件：{self.config_path}")

        # 尝试获取API代理实例
        self.api_proxy = None
        if has_api_proxy:
            try:
                import sys
                # 导入api_proxy实例
                sys.path.append(os.path.join(os.path.dirname(__file__), "..", ".."))
                from admin.server import get_api_proxy
                self.api_proxy = get_api_proxy()
                if self.api_proxy:
                    logger.info("成功获取API代理实例")
                else:
                    logger.warning("API代理实例获取失败，将使用直接连接")
            except Exception as e:
                logger.error(f"获取API代理实例失败: {e}")
                logger.error(traceback.format_exc())

    def get_user_model(self, user_id: str) -> ModelConfig:
        """获取用户当前使用的模型"""
        if self.remember_user_model and user_id in self.user_models:
            return self.user_models[user_id]
        return self.current_model

    def set_user_model(self, user_id: str, model: ModelConfig):
        """设置用户当前使用的模型"""
        if self.remember_user_model:
            self.user_models[user_id] = model

    def get_conversation_id(self, user_id: str) -> str:
        """获取或创建会话ID，支持1分钟内的连续对话"""
        current_time = time.time()

        # 清理过期的会话
        expired_users = []
        for uid, session_data in self.conversation_sessions.items():
            if current_time - session_data["timestamp"] > self.conversation_timeout:
                expired_users.append(uid)

        for uid in expired_users:
            del self.conversation_sessions[uid]
            logger.debug(f"清理过期会话: {uid}")

        # 检查用户是否有有效的会话
        if user_id in self.conversation_sessions:
            session_data = self.conversation_sessions[user_id]
            if current_time - session_data["timestamp"] <= self.conversation_timeout:
                # 更新时间戳
                session_data["timestamp"] = current_time
                logger.debug(f"使用现有会话ID: {session_data['chat_id']} (用户: {user_id})")
                return session_data["chat_id"]
            else:
                # 会话已过期，删除
                del self.conversation_sessions[user_id]
                logger.debug(f"会话已过期，删除: {user_id}")

        # 创建新的会话ID
        new_chat_id = str(uuid.uuid4())
        self.conversation_sessions[user_id] = {
            "chat_id": new_chat_id,
            "timestamp": current_time
        }
        logger.info(f"创建新会话ID: {new_chat_id} (用户: {user_id})")
        return new_chat_id

    def reset_conversation_session(self, user_id: str):
        """重置用户的会话"""
        if user_id in self.conversation_sessions:
            del self.conversation_sessions[user_id]
            logger.info(f"重置用户会话: {user_id}")

    def update_conversation_timestamp(self, user_id: str):
        """更新会话时间戳"""
        if user_id in self.conversation_sessions:
            self.conversation_sessions[user_id]["timestamp"] = time.time()
            logger.debug(f"更新会话时间戳: {user_id}")

    def is_message_processed(self, message: dict) -> bool:
        """检查消息是否已经处理过"""
        # 清理过期的消息记录
        current_time = time.time()
        expired_keys = []
        for msg_id, timestamp in self.processed_messages.items():
            if current_time - timestamp > self.message_expiry:
                expired_keys.append(msg_id)

        for key in expired_keys:
            del self.processed_messages[key]

        # 获取消息ID
        msg_id = message.get("MsgId") or message.get("NewMsgId")
        if not msg_id:
            return False  # 如果没有消息ID，视为未处理过

        # 检查消息是否已处理
        return msg_id in self.processed_messages

    def mark_message_processed(self, message: dict):
        """标记消息为已处理"""
        msg_id = message.get("MsgId") or message.get("NewMsgId")
        if msg_id:
            self.processed_messages[msg_id] = time.time()
            logger.debug(f"标记消息 {msg_id} 为已处理")

    def get_model_from_message(self, content: str, user_id: str) -> tuple[ModelConfig, str, bool]:
        """根据消息内容判断使用哪个模型，并返回是否是切换模型的命令"""
        original_content = content  # 保留原始内容
        content = content.lower()  # 只在检测时使用小写版本

        # 检查是否是切换模型的命令
        if content.endswith("切换"):
            for model_name, model_config in self.models.items():
                for trigger in model_config.trigger_words:
                    if content.startswith(trigger.lower()):
                        self.set_user_model(user_id, model_config)
                        logger.info(f"用户 {user_id} 切换模型到 {model_name}")
                        return model_config, "", True
            return self.get_user_model(user_id), original_content, False

        # 检查是否使用了唤醒词
        logger.debug(f"检查消息 '{content}' 是否包含唤醒词")
        for wakeup_word, model_config in self.wakeup_word_to_model.items():
            wakeup_lower = wakeup_word.lower()
            content_lower = content.lower()
            if content_lower.startswith(wakeup_lower) or f" {wakeup_lower}" in content_lower:
                model_name = next((name for name, config in self.models.items() if config == model_config), '未知')
                logger.info(f"消息中检测到唤醒词 '{wakeup_word}'，临时使用模型 '{model_name}'")

                # 更精确地替换唤醒词
                original_wakeup = None
                if content_lower.startswith(wakeup_lower):
                    original_wakeup = original_content[:len(wakeup_lower)]
                else:
                    wakeup_pos = content_lower.find(f" {wakeup_lower}") + 1
                    if wakeup_pos > 0:
                        original_wakeup = original_content[wakeup_pos:wakeup_pos+len(wakeup_lower)]

                if original_wakeup:
                    query = original_content.replace(original_wakeup, "", 1).strip()
                    logger.debug(f"唤醒词处理后的查询: '{query}'")
                    return model_config, query, False

        # 检查是否是临时使用其他模型
        for model_name, model_config in self.models.items():
            for trigger in model_config.trigger_words:
                if trigger.lower() in content:
                    logger.info(f"消息中包含触发词 '{trigger}'，临时使用模型 '{model_name}'")
                    query = original_content.replace(trigger, "", 1).strip()
                    return model_config, query, False

        # 使用用户当前的模型
        current_model = self.get_user_model(user_id)
        model_name = next((name for name, config in self.models.items() if config == current_model), '默认')
        logger.debug(f"未检测到特定模型指示，使用用户 {user_id} 当前默认模型 '{model_name}'")
        return current_model, original_content, False

    async def reset_conversation(self, bot: WechatAPIClient, message: dict, model_config=None):
        """重置与FastGPT的对话

        Args:
            bot: WechatAPIClient实例
            message: 消息字典
            model_config: 模型配置（可选）

        Returns:
            bool: 是否成功重置对话
        """
        try:
            # 使用传入的model_config，如果没有则使用默认模型
            model = model_config or self.current_model

            # 获取用户ID
            user_id = message["FromWxid"]
            if message.get("IsGroup", False):
                # 群聊消息，使用群聊ID
                user_id = message["FromWxid"]
            else:
                # 私聊消息，使用发送者ID
                user_id = message["SenderWxid"]

            # 重置本地会话管理
            self.reset_conversation_session(user_id)

            # 从数据库清除会话ID（保持兼容性）
            self.db.save_llm_thread_id(user_id, "", "fastgpt")
            logger.success(f"成功重置用户 {user_id} 的FastGPT对话")
            return True

        except Exception as e:
            logger.error(f"重置对话时发生错误: {e}")
            logger.error(traceback.format_exc())
            return False

    async def _check_point(self, bot: WechatAPIClient, message: dict, model: ModelConfig) -> bool:
        """检查用户积分是否足够 (已禁用)"""
        return True

    def is_at_message(self, message: dict) -> bool:
        """检查是否是@消息"""
        return message.get("IsGroup", False) and message.get("IsAt", False)

    async def get_cached_images(self, user_id: str) -> List[bytes]:
        """获取缓存的所有图片内容"""
        current_time = time.time()
        if user_id in self.image_cache:
            cached_images = self.image_cache[user_id]
            valid_images = []

            # 过滤出未过期的图片
            for img_data in cached_images:
                if current_time - img_data["timestamp"] <= self.image_cache_timeout:
                    valid_images.append(img_data["content"])

            # 更新缓存，只保留未过期的图片
            if valid_images:
                self.image_cache[user_id] = [
                    img_data for img_data in cached_images
                    if current_time - img_data["timestamp"] <= self.image_cache_timeout
                ]
                logger.debug(f"返回用户 {user_id} 的 {len(valid_images)} 张缓存图片")
                return valid_images
            else:
                # 所有图片都过期了，删除缓存
                del self.image_cache[user_id]
                logger.debug(f"用户 {user_id} 的所有图片缓存都已过期")
        return []

    async def get_cached_image(self, user_id: str) -> Optional[bytes]:
        """获取缓存的最后一张图片内容（保持向后兼容）"""
        images = await self.get_cached_images(user_id)
        return images[-1] if images else None

    async def download_and_process_image(self, bot: WechatAPIClient, message: dict) -> Optional[bytes]:
        """下载并处理图片"""
        try:
            # 从消息中获取图片信息
            xml_content = message.get("Content", "")
            if not xml_content:
                logger.warning("图片消息没有XML内容")
                return None

            # 解析XML获取图片信息
            try:
                import xml.etree.ElementTree as ET
                # 清理XML内容，移除可能的非法字符
                xml_content = xml_content.strip()

                # 检查XML格式
                if not xml_content.startswith('<?xml'):
                    logger.warning(f"XML内容格式异常，尝试直接使用最新图片文件")
                    # 直接使用备用方案
                    raise ET.ParseError("XML格式异常")

                logger.debug(f"准备解析XML: {xml_content[:200]}...")
                root = ET.fromstring(xml_content)
                img_element = root.find('img')
                if img_element is None:
                    logger.warning("XML中没有找到img元素")
                    raise ET.ParseError("未找到img元素")

                # 获取图片的MD5和其他信息
                md5 = img_element.get('md5')
                aeskey = img_element.get('aeskey')
                cdnthumburl = img_element.get('cdnthumburl')

                if not md5:
                    logger.warning("无法获取图片MD5")
                    raise ET.ParseError("无法获取MD5")

                logger.info(f"解析到图片信息: MD5={md5}, AESKey={aeskey[:20] if aeskey else 'None'}...")

                # 检查图片文件是否已经下载到本地
                image_path = f"/app/files/{md5}.jpeg"
                if os.path.exists(image_path):
                    logger.info(f"找到本地图片文件: {image_path}")
                    with open(image_path, 'rb') as f:
                        image_data = f.read()
                    logger.info(f"成功读取图片文件，大小: {len(image_data)} 字节")
                    return image_data
                else:
                    logger.warning(f"本地图片文件不存在: {image_path}")
                    # 如果文件不存在，使用备用方案
                    raise ET.ParseError("本地文件不存在")

            except ET.ParseError as e:
                logger.error(f"解析图片XML失败: {e}")
                logger.error(f"XML内容: {xml_content}")

                # 尝试从日志中获取MD5（作为备用方案）
                # 从日志可以看到图片已经保存，我们可以尝试直接使用最新的图片
                try:
                    # 获取files目录下最新的jpeg文件
                    files_dir = "/app/files"
                    if os.path.exists(files_dir):
                        jpeg_files = [f for f in os.listdir(files_dir) if f.endswith('.jpeg')]
                        if jpeg_files:
                            # 按修改时间排序，获取最新的文件
                            jpeg_files.sort(key=lambda x: os.path.getmtime(os.path.join(files_dir, x)), reverse=True)
                            latest_file = os.path.join(files_dir, jpeg_files[0])
                            logger.info(f"使用最新的图片文件: {latest_file}")
                            with open(latest_file, 'rb') as f:
                                image_data = f.read()
                            logger.info(f"成功读取最新图片文件，大小: {len(image_data)} 字节")
                            return image_data
                except Exception as fallback_e:
                    logger.error(f"备用方案也失败: {fallback_e}")

                return None

        except Exception as e:
            logger.error(f"下载图片失败: {e}")
            logger.error(traceback.format_exc())
            return None

    @on_text_message(priority=25)
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        if not self.enable:
            return True

        content = message["Content"].strip()
        command = content.split(" ")[0] if content else ""

        # 处理重置对话命令
        if command == "重置对话":
            # 获取用户当前使用的模型
            model = self.get_user_model(message["SenderWxid"])

            # 执行重置对话操作
            success = await self.reset_conversation(bot, message, model)

            if success:
                # 重置成功，发送通知
                if message.get("IsGroup", False):
                    await bot.send_at_message(
                        message["FromWxid"],
                        "\n对话已重置，我已经忘记了之前的对话内容。",
                        [message["SenderWxid"]]
                    )
                else:
                    await bot.send_text_message(
                        message["FromWxid"],
                        "对话已重置，我已经忘记了之前的对话内容。"
                    )
            else:
                # 重置失败，发送通知
                if message.get("IsGroup", False):
                    await bot.send_at_message(
                        message["FromWxid"],
                        "\n重置对话失败，可能是因为没有活跃的对话或发生了错误。",
                        [message["SenderWxid"]]
                    )
                else:
                    await bot.send_text_message(
                        message["FromWxid"],
                        "重置对话失败，可能是因为没有活跃的对话或发生了错误。"
                    )
            return False

        if not message["IsGroup"]:
            # 先检查唤醒词或触发词，获取对应模型
            model, processed_query, is_switch = self.get_model_from_message(content, message["SenderWxid"])

            # 检查是否有最近的图片
            image_contents = await self.get_cached_images(message["FromWxid"])
            files = []
            if image_contents:
                try:
                    logger.debug(f"发现 {len(image_contents)} 张最近的图片，准备处理")
                    files = image_contents  # 直接传递图片字节数据列表
                except Exception as e:
                    logger.error(f"处理图片失败: {e}")

            if command in self.commands:
                query = content[len(command):].strip()
            else:
                query = content

            # 检查API密钥是否可用 - 使用检测到的模型，而非默认模型
            if query and model.api_key:
                if await self._check_point(bot, message, model):
                    if is_switch:
                        model_name = next(name for name, config in self.models.items() if config == model)
                        await bot.send_text_message(
                            message["FromWxid"],
                            f"已切换到{model_name.upper()}模型，将一直使用该模型直到下次切换。"
                        )
                        return False
                    # 使用获取到的模型处理请求
                    await self.fastgpt_chat(bot, message, processed_query, files=files, specific_model=model)
                else:
                    logger.info(f"积分检查失败或模型API密钥无效，无法处理请求")
            else:
                if not query:
                    logger.debug("查询内容为空，不处理")
                elif not model.api_key:
                    logger.error(f"模型 {next((name for name, config in self.models.items() if config == model), '未知')} 的API密钥未配置")
                    await bot.send_text_message(message["FromWxid"], "所选模型的API密钥未配置，请联系管理员")
            return False

        # 以下是群聊处理逻辑
        group_id = message["FromWxid"]
        user_wxid = message["SenderWxid"]

        # 添加对切换模型命令的特殊处理
        if content.endswith("切换"):
            for model_name, model_config in self.models.items():
                for trigger in model_config.trigger_words:
                    if content.lower().startswith(trigger.lower()):
                        self.set_user_model(user_wxid, model_config)
                        await bot.send_at_message(
                            group_id,
                            f"\n已切换到{model_name.upper()}模型，将一直使用该模型直到下次切换。",
                            [user_wxid]
                        )
                        return False

        # 处理群聊中的重置对话命令
        if command == "重置对话":
            # 获取用户当前使用的模型
            model = self.get_user_model(user_wxid)

            # 执行重置对话操作
            success = await self.reset_conversation(bot, message, model)

            if success:
                # 重置成功，发送通知
                await bot.send_at_message(
                    group_id,
                    "\n对话已重置，我已经忘记了之前的对话内容。",
                    [user_wxid]
                )
            else:
                # 重置失败，发送通知
                await bot.send_at_message(
                    group_id,
                    "\n重置对话失败，可能是因为没有活跃的对话或发生了错误。",
                    [user_wxid]
                )
            return False

        is_at = self.is_at_message(message)
        is_command = command in self.commands

        # 先检查是否有唤醒词
        wakeup_detected = False
        wakeup_model = None
        processed_wakeup_query = ""

        for wakeup_word, model_config in self.wakeup_word_to_model.items():
            # 改用更精确的匹配方式，避免错误识别
            wakeup_lower = wakeup_word.lower()
            content_lower = content.lower()
            if content_lower.startswith(wakeup_lower) or f" {wakeup_lower}" in content_lower:
                wakeup_detected = True
                wakeup_model = model_config
                model_name = next((name for name, config in self.models.items() if config == model_config), '未知')
                logger.info(f"检测到唤醒词 '{wakeup_word}'，触发模型 '{model_name}'，原始内容: '{content}'")

                # 更精确地替换唤醒词
                original_wakeup = None
                if content_lower.startswith(wakeup_lower):
                    original_wakeup = content[:len(wakeup_lower)]
                else:
                    wakeup_pos = content_lower.find(f" {wakeup_lower}") + 1
                    if wakeup_pos > 0:
                        original_wakeup = content[wakeup_pos:wakeup_pos+len(wakeup_lower)]

                if original_wakeup:
                    processed_wakeup_query = content.replace(original_wakeup, "", 1).strip()
                    logger.debug(f"唤醒词处理后的查询: '{processed_wakeup_query}'")
                break

        if wakeup_detected and wakeup_model:
            # 检查是否有最近的图片
            image_contents = await self.get_cached_images(group_id)
            files = []
            if image_contents:
                try:
                    logger.debug(f"发现 {len(image_contents)} 张最近的图片，准备处理")
                    files = image_contents  # 直接传递图片字节数据列表
                except Exception as e:
                    logger.error(f"处理图片失败: {e}")

            if processed_wakeup_query:
                # 使用唤醒词对应的模型
                model = wakeup_model
                if await self._check_point(bot, message, model):
                    await self.fastgpt_chat(bot, message, processed_wakeup_query, files=files, specific_model=model)
                return False

        if is_at or is_command:
            # 检查是否有最近的图片
            image_contents = await self.get_cached_images(group_id)
            files = []
            if image_contents:
                try:
                    logger.debug(f"发现 {len(image_contents)} 张最近的图片，准备处理")
                    files = image_contents  # 直接传递图片字节数据列表
                except Exception as e:
                    logger.error(f"处理图片失败: {e}")

            if is_command:
                query = content[len(command):].strip()
            else:
                query = content

            if query:
                # 检查是否有唤醒词或触发词
                model, processed_query, is_switch = self.get_model_from_message(query, message["SenderWxid"])
                if is_switch:
                    model_name = next(name for name, config in self.models.items() if config == model)
                    await bot.send_at_message(
                        message["FromWxid"],
                        f"\n已切换到{model_name.upper()}模型，将一直使用该模型直到下次切换。",
                        [message["SenderWxid"]]
                    )
                    return False

                if await self._check_point(bot, message, model):
                    await self.fastgpt_chat(bot, message, processed_query, files=files, specific_model=model)
        return False



    @on_at_message(priority=25)
    async def handle_at(self, bot: WechatAPIClient, message: dict):
        """处理@消息"""
        if not self.enable:
            return True

        # 检查消息是否已经处理过
        if self.is_message_processed(message):
            logger.info(f"消息 {message.get('MsgId') or message.get('NewMsgId')} 已经处理过，跳过")
            return False

        # 标记消息为已处理
        self.mark_message_processed(message)

        content = message["Content"].strip()

        # 检查是否有最近的图片
        image_contents = await self.get_cached_images(message["FromWxid"])
        files = []
        if image_contents:
            try:
                logger.debug(f"发现 {len(image_contents)} 张最近的图片，准备处理")
                files = image_contents  # 直接传递图片字节数据列表
            except Exception as e:
                logger.error(f"处理图片失败: {e}")

        # 先检查唤醒词或触发词，获取对应模型
        model, processed_query, is_switch = self.get_model_from_message(content, message["SenderWxid"])

        if is_switch:
            model_name = next(name for name, config in self.models.items() if config == model)
            await bot.send_at_message(
                message["FromWxid"],
                f"\n已切换到{model_name.upper()}模型，将一直使用该模型直到下次切换。",
                [message["SenderWxid"]]
            )
            return False

        if await self._check_point(bot, message, model):
            # 使用上面已经获取的模型和处理过的查询
            logger.info(f"@消息使用模型 '{next((name for name, config in self.models.items() if config == model), '未知')}' 处理请求")
            await self.fastgpt_chat(bot, message, processed_query, files=files, specific_model=model)
        else:
            logger.info(f"积分检查失败，无法处理@消息请求")
        return False

    @on_image_message(priority=25)
    async def handle_image(self, bot: WechatAPIClient, message: dict):
        """处理图片消息"""
        if not self.enable:
            return True

        # 获取图片内容并缓存
        image_content = await self.download_and_process_image(bot, message)
        if image_content:
            user_id = message["FromWxid"]
            current_time = time.time()

            # 初始化用户的图片缓存列表
            if user_id not in self.image_cache:
                self.image_cache[user_id] = []

            # 添加新图片到缓存列表
            self.image_cache[user_id].append({
                "content": image_content,
                "timestamp": current_time
            })

            # 清理过期的图片缓存
            self.image_cache[user_id] = [
                img_data for img_data in self.image_cache[user_id]
                if current_time - img_data["timestamp"] <= self.image_cache_timeout
            ]

            logger.info(f"已缓存用户 {user_id} 的图片，当前共有 {len(self.image_cache[user_id])} 张图片")
        return True

    @on_voice_message(priority=25)
    async def handle_voice(self, bot: WechatAPIClient, message: dict):
        """处理语音消息"""
        if not self.enable:
            return True

        try:
            # 这里需要实现语音转文字功能
            # 暂时跳过语音处理
            logger.info("收到语音消息，暂时跳过处理")
            return True
        except Exception as e:
            logger.error(f"处理语音消息失败: {e}")
            return True





    async def fastgpt_chat(self, bot: WechatAPIClient, message: dict, query: str, files: list = None, specific_model: ModelConfig = None):
        """与FastGPT进行对话"""
        try:
            # 使用指定的模型，如果没有则使用当前模型
            model = specific_model or self.current_model

            logger.debug(f"开始调用 FastGPT API - 用户消息: {query}")
            logger.debug(f"文件列表: {files}")

            # 获取会话ID - 使用新的会话管理
            user_wxid = message["SenderWxid"]
            from_wxid = message["FromWxid"]

            # 确定会话用户ID
            if message["IsGroup"]:
                # 群聊消息，使用群聊ID作为会话标识
                session_user_id = from_wxid
                logger.debug(f"群聊消息，使用群聊ID '{from_wxid}' 作为会话标识")
            else:
                # 私聊消息，使用FromWxid作为会话标识
                session_user_id = from_wxid
                logger.debug(f"私聊消息，使用FromWxid '{from_wxid}' 作为会话标识")

            # 使用新的会话管理获取会话ID
            conversation_id = self.get_conversation_id(session_user_id)

            logger.debug(f"当前会话ID: {conversation_id}")

            # 构建请求数据
            headers = {
                "Authorization": f"Bearer {model.api_key}",
                "Content-Type": "application/json"
            }

            # 构建消息内容
            if files and len(files) > 0:
                # 如果有图片文件，构建多模态消息
                content_parts = []

                # 添加文本内容
                if query.strip():
                    content_parts.append({
                        "type": "text",
                        "text": query
                    })

                # 添加图片内容
                for file_data in files:
                    if isinstance(file_data, bytes):
                        # 将图片转换为base64
                        import base64
                        image_base64 = base64.b64encode(file_data).decode('utf-8')
                        content_parts.append({
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{image_base64}"
                            }
                        })
                        logger.debug("添加图片到消息中")

                messages = [{
                    "role": "user",
                    "content": content_parts if len(content_parts) > 1 else query
                }]
            else:
                # 纯文本消息
                messages = [{"role": "user", "content": query}]

            data = {
                "chatId": conversation_id,  # 直接使用获取到的会话ID
                "stream": False,
                "detail": False,
                "messages": messages
            }

            # 如果有app_id，添加到请求中
            if hasattr(model, 'app_id') and model.app_id:
                data["appId"] = model.app_id

            # 构建API URL - FastGPT使用 /v1/chat/completions 端点
            url = f"{model.base_url}/v1/chat/completions"

            logger.debug(f"请求URL: {url}")
            logger.debug(f"请求数据: {json.dumps(data, ensure_ascii=False, indent=2)}")

            # 发送请求
            async with aiohttp.ClientSession() as session:
                proxy = self.http_proxy if self.http_proxy and self.http_proxy.strip() else None
                async with session.post(url, headers=headers, json=data, proxy=proxy) as resp:
                    if resp.status == 200:
                        result = await resp.json()
                        logger.debug(f"FastGPT API响应: {json.dumps(result, ensure_ascii=False, indent=2)}")

                        # 解析响应
                        if "choices" in result and len(result["choices"]) > 0:
                            ai_resp = result["choices"][0]["message"]["content"]

                            # 更新会话时间戳，保持连续对话
                            self.update_conversation_timestamp(session_user_id)

                            # 保存新的会话ID（如果有，保持兼容性）
                            if "chatId" in result:
                                new_conversation_id = result["chatId"]
                                if new_conversation_id != conversation_id:
                                    # 更新本地会话管理中的会话ID
                                    if session_user_id in self.conversation_sessions:
                                        self.conversation_sessions[session_user_id]["chat_id"] = new_conversation_id
                                        logger.debug(f"更新会话ID: {new_conversation_id} (用户: {session_user_id})")

                                    # 保持数据库兼容性
                                    self.db.save_llm_thread_id(session_user_id, new_conversation_id, "fastgpt")

                            # 发送回复
                            if message.get("IsGroup", False):
                                await bot.send_at_message(
                                    message["FromWxid"],
                                    f"\n{ai_resp}",
                                    [message["SenderWxid"]]
                                )
                            else:
                                await bot.send_text_message(
                                    message["FromWxid"],
                                    ai_resp
                                )
                        else:
                            logger.error("FastGPT API返回格式异常")
                            await bot.send_text_message(
                                message["FromWxid"],
                                f"{FASTGPT_ERROR_MESSAGE}API返回格式异常"
                            )
                    else:
                        error_text = await resp.text()
                        logger.error(f"FastGPT API请求失败: HTTP {resp.status} - {error_text}")

                        # 如果是400错误，可能是会话ID无效，尝试重置
                        if resp.status == 400:
                            logger.warning("收到400错误，重置会话ID")

                            # 重置本地会话管理
                            self.reset_conversation_session(session_user_id)

                            # 保持数据库兼容性
                            self.db.save_llm_thread_id(session_user_id, "", "fastgpt")

                            await bot.send_text_message(
                                message["FromWxid"],
                                f"{XYBOT_PREFIX}检测到对话异常，已重置对话。正在重新处理您的问题..."
                            )

                            # 重新调用，但不递归太深
                            if not hasattr(self, '_retry_count'):
                                self._retry_count = 0
                            if self._retry_count < 1:
                                self._retry_count += 1
                                await self.fastgpt_chat(bot, message, query, files, specific_model)
                                self._retry_count = 0
                        else:
                            await bot.send_text_message(
                                message["FromWxid"],
                                f"{FASTGPT_ERROR_MESSAGE}HTTP {resp.status}"
                            )

        except Exception as e:
            logger.error(f"FastGPT对话时发生错误: {e}")
            logger.error(traceback.format_exc())
            await bot.send_text_message(
                message["FromWxid"],
                f"{FASTGPT_ERROR_MESSAGE}{str(e)}"
            )
